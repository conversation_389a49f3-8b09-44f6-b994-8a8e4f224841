import time
import os
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC

def main():
    # Get the absolute path to the Chrome executable
    # chrome_path = os.path.abspath("fingerprint-chromium/Chromium/Application/chrome.exe")
    chrome_path = os.path.abspath("driver/chromedriver.exe")

    # Set up Chrome options
    chrome_options = Options()
    chrome_options.binary_location = chrome_path

    # Add proxy configuration
    # chrome_options.add_argument("--proxy-server=http://gw.dataimpulse.com:823")
    # chrome_options.add_argument("--timezone=America/Los_Angeles")
    # chrome_options.add_argument("--lang=en-US")
    # chrome_options.add_argument("--accept-lang=en-US")
    # chrome_options.add_argument("--fingerprint=1003")

    # Add anti-detection arguments
    chrome_options.add_argument("--disable-blink-features=AutomationControlled")
    chrome_options.add_argument("--disable-dev-shm-usage")
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-setuid-sandbox")
    chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
    chrome_options.add_experimental_option('useAutomationExtension', False)

    # Enable logging to capture console output
    chrome_options.add_argument("--enable-logging")
    chrome_options.add_argument("--log-level=0")

    # Create Chrome service (you can specify chromedriver path if needed)
    # service = Service("path/to/chromedriver.exe")  # Uncomment and set path if needed
    service = Service()  # Uses chromedriver from PATH

    # Launch Chrome browser
    print("Launching Chrome browser...")
    driver = webdriver.Chrome(service=service, options=chrome_options)

    # Execute script to disable webdriver detection
    driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")

    try:
        # Navigate to the website
        print("Opening https://www.genspark.ai...")
        driver.get("https://www.genspark.ai")

        # Wait a bit for the page to load
        time.sleep(5)

        # Execute reCAPTCHA using JavaScript
        print("Executing reCAPTCHA...")
        try:
            # Execute the reCAPTCHA code and capture the token
            script = """
            return new Promise((resolve) => {
                grecaptcha.ready(function() {
                    grecaptcha.execute('6Leq7KYqAAAAAGdd1NaUBJF9dHTPAKP7DcnaRc66', {action: 'submit'}).then(function(token) {
                        console.log('reCAPTCHA Token:', token);
                        resolve(token);
                    });
                });
            });
            """

            # Execute the script and get the token
            token = driver.execute_async_script(script)
            print(f"reCAPTCHA Token: {token}")

        except Exception as e:
            print(f"Error executing reCAPTCHA: {e}")

            # Alternative method: execute without waiting for return value
            try:
                driver.execute_script("""
                    grecaptcha.ready(function() {
                        grecaptcha.execute('6Leq7KYqAAAAAGdd1NaUBJF9dHTPAKP7DcnaRc66', {action: 'submit'}).then(function(token) {
                            console.log('reCAPTCHA Token:', token);
                            window.recaptchaToken = token;
                        });
                    });
                """)
                print("reCAPTCHA execution initiated (alternative method)...")

                # Try to get the token from window object after a short wait
                time.sleep(3)
                try:
                    token = driver.execute_script("return window.recaptchaToken;")
                    if token:
                        print(f"reCAPTCHA Token: {token}")
                except:
                    print("Could not retrieve token from window object")

            except Exception as e2:
                print(f"Alternative method also failed: {e2}")

        # Wait for 9999 seconds
        print("Waiting for 9999 seconds...")
        time.sleep(9999)

    finally:
        # Close the browser
        print("Closing browser...")
        driver.quit()

if __name__ == "__main__":
    main()